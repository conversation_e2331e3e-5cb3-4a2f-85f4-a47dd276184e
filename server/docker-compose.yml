services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: colour-scan-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: colour_and_scan_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - colour-scan-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: colour-scan-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - colour-scan-network

  # MinIO for object storage
  minio:
    image: minio/minio:latest
    container_name: colour-scan-minio
    restart: unless-stopped
    ports:
      - '9000:9000'
      - '9001:9001'
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - colour-scan-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # NestJS Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: colour-scan-app
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: production
      PORT: 3000
      API_PREFIX: api/v1
      DATABASE_URL: ********************************************/colour_and_scan_db?schema=public
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ''
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-this-in-production
      JWT_REFRESH_EXPIRES_IN: 30d
      THROTTLE_TTL: 60
      THROTTLE_LIMIT: 10
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001
      LOG_LEVEL: info
      MAX_FILE_SIZE: 10485760
      UPLOAD_DEST: ./uploads
      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_USE_SSL: false
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      MINIO_PAPERS_BUCKET: papers
      MINIO_ASSETS_BUCKET: character-assets
      MINIO_TEMP_BUCKET: temp-uploads
    volumes:
      - app_uploads:/app/uploads
    networks:
      - colour-scan-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  minio_data:
  app_uploads:

networks:
  colour-scan-network:
    driver: bridge
