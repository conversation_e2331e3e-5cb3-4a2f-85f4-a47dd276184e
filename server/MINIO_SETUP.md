# MinIO Setup for Paper Upload and Character Asset Management

This document describes the MinIO integration for managing uploaded papers and character-based assets in the Colour and Scan application.

## Overview

The application uses MinIO as an S3-compatible object storage solution to handle:
- **Paper Uploads**: User-uploaded papers (images, PDFs) for character generation
- **Character Assets**: Generated sprites, animations, textures, masks, and thumbnails
- **Temporary Files**: Temporary uploads and processing files

## Architecture

### Storage Buckets

1. **Papers Bucket** (`papers`): Stores original uploaded papers
2. **Assets Bucket** (`character-assets`): Stores generated character assets
3. **Temp Bucket** (`temp-uploads`): Temporary files and processing artifacts

### Key Components

- **StorageService**: Core MinIO operations (upload, download, delete, list)
- **PapersService**: Paper upload and management
- **CharacterAssetsService**: Asset generation and management
- **FileProcessingProcessor**: Queue-based file processing
- **CleanupService**: Automated cleanup and maintenance

## Setup Instructions

### 1. Environment Configuration

Add the following to your `.env` file:

```env
# MinIO Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9001
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_PAPERS_BUCKET=papers
MINIO_ASSETS_BUCKET=character-assets
MINIO_TEMP_BUCKET=temp-uploads

# Storage Configuration
STORAGE_RETENTION_DAYS=90
MAX_USER_STORAGE=1073741824
COMPRESSION_ENABLED=false
COMPRESSION_QUALITY=80
```

### 2. Docker Setup

#### Development
```bash
# Start development services (includes MinIO)
npm run docker:dev

# MinIO Console will be available at:
# http://localhost:9002 (admin interface)
# http://localhost:9001 (API endpoint)
```

#### Production
```bash
# Start production services
npm run docker:prod

# MinIO Console will be available at:
# http://localhost:9001 (admin interface)
# http://localhost:9000 (API endpoint)
```

### 3. Manual MinIO Setup

If running MinIO separately:

```bash
# Pull MinIO image
docker pull minio/minio:latest

# Run MinIO server
docker run -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  -v minio_data:/data \
  minio/minio server /data --console-address ":9001"
```

## API Endpoints

### Storage Management

- `POST /api/v1/storage/upload/:bucket` - Upload file to bucket
- `GET /api/v1/storage/download/:bucket/:key` - Download file
- `DELETE /api/v1/storage/:bucket/:key` - Delete file
- `GET /api/v1/storage/list/:bucket` - List files in bucket
- `POST /api/v1/storage/url/upload/:bucket` - Generate upload URL
- `POST /api/v1/storage/url/download/:bucket` - Generate download URL

### Paper Management

- `POST /api/v1/papers/upload` - Upload paper for processing
- `GET /api/v1/papers` - Get user papers with pagination
- `GET /api/v1/papers/:id` - Get paper with assets
- `PUT /api/v1/papers/:id/status` - Update paper status
- `DELETE /api/v1/papers/:id` - Delete paper and assets

### Character Assets

- `POST /api/v1/character-assets/papers/:paperId/assets` - Create asset from file
- `POST /api/v1/character-assets/papers/:paperId/generate` - Generate asset from paper
- `GET /api/v1/character-assets/papers/:paperId` - Get paper assets
- `GET /api/v1/character-assets/user/assets` - Get user assets
- `POST /api/v1/character-assets/:id/thumbnail` - Generate thumbnail
- `POST /api/v1/character-assets/:id/animation-frames` - Generate animation

## File Processing Workflow

1. **Upload**: User uploads paper via `/papers/upload`
2. **Storage**: File stored in papers bucket with metadata
3. **Queue**: Processing job queued for background processing
4. **Processing**: File processed, basic assets generated
5. **Assets**: Generated assets stored in assets bucket
6. **Notification**: User notified via WebSocket

## Asset Types

- **SPRITE**: Character sprite images
- **ANIMATION**: Animation frame sequences
- **TEXTURE**: Texture maps and materials
- **MASK**: Alpha masks and overlays
- **THUMBNAIL**: Preview thumbnails

## Security Features

### File Validation
- MIME type checking
- File size limits
- Magic number validation
- Suspicious filename detection
- Extension validation

### Access Control
- JWT authentication required
- User-scoped access to files
- Pre-signed URL generation
- Bucket-level permissions

## Cleanup and Maintenance

### Automated Cleanup
- **Hourly**: Temporary files older than 1 hour
- **Daily**: Failed uploads older than 24 hours
- **Daily**: Processed files older than retention period
- **Daily**: Orphaned files not in database

### Manual Cleanup
```typescript
// Cleanup user data
await cleanupService.cleanupUserData(userId);

// Get storage statistics
const stats = await cleanupService.getStorageStats();
```

## Monitoring and Logging

### WebSocket Notifications
- Upload progress updates
- Processing status changes
- Asset generation completion
- Error notifications

### Logging
- File upload/download operations
- Processing job status
- Cleanup operations
- Error tracking

## Configuration Options

### File Limits
- `MAX_FILE_SIZE`: Maximum upload size (default: 10MB)
- `MAX_USER_STORAGE`: Per-user storage limit (default: 1GB)

### Retention
- `STORAGE_RETENTION_DAYS`: File retention period (default: 90 days)

### Compression
- `COMPRESSION_ENABLED`: Enable image compression
- `COMPRESSION_QUALITY`: Compression quality (1-100)

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check MinIO service is running
   - Verify endpoint and port configuration
   - Check network connectivity

2. **Access Denied**
   - Verify access key and secret key
   - Check bucket permissions
   - Ensure buckets exist

3. **Upload Failures**
   - Check file size limits
   - Verify MIME type restrictions
   - Check available storage space

### Health Checks

```bash
# Check MinIO health
curl -f http://localhost:9000/minio/health/live

# Check bucket existence
curl -X HEAD http://localhost:9000/papers
```

## Development

### Testing
```bash
# Run with test MinIO instance
npm run test:e2e

# Test file upload
curl -X POST -F "file=@test.png" \
  -H "Authorization: Bearer <token>" \
  http://localhost:3000/api/v1/papers/upload
```

### Database Migration
```bash
# Generate Prisma client after schema changes
npm run prisma:generate

# Apply database migrations
npm run prisma:migrate
```

## Production Considerations

1. **Backup Strategy**: Regular backup of MinIO data
2. **Scaling**: Consider MinIO clustering for high availability
3. **SSL/TLS**: Enable SSL for production deployments
4. **Monitoring**: Set up monitoring and alerting
5. **Performance**: Tune MinIO configuration for your workload
