import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { ScheduleModule } from '@nestjs/schedule';
import { LoggerModule } from 'nestjs-pino';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

import configuration from 'src/config/configuration';
import { EnvironmentVariables } from 'src/config/validation';
import { LoggerMiddleware } from 'src/common/middleware/logger.middleware';
import { DatabaseModule } from 'src/database/database.module';
import { QueueModule } from 'src/queue/queue.module';
import { UsersModule } from 'src/modules/users/users.module';
import { AuthModule } from 'src/modules/auth/auth.module';
import { WebSocketModule } from 'src/modules/websocket/websocket.module';
import { SchedulerModule } from 'src/modules/scheduler/scheduler.module';
import { HealthModule } from 'src/modules/health/health.module';
import { UsersController } from 'src/modules/users/users.controller';
import { AuthController } from 'src/modules/auth/auth.controller';
import { HealthController } from 'src/modules/health/health.controller';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      validate: async (config: Record<string, unknown>) => {
        const validatedConfig = plainToInstance(EnvironmentVariables, config, {
          enableImplicitConversion: true,
        });
        const errors = await validate(validatedConfig, {
          skipMissingProperties: false,
        });
        if (errors.length > 0) {
          throw new Error(errors.toString());
        }
        return validatedConfig;
      },
    }),

    // Logging
    LoggerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        pinoHttp: {
          level: configService.get<string>('logging.level'),
          transport:
            configService.get<string>('nodeEnv') !== 'production'
              ? {
                  target: 'pino-pretty',
                  options: {
                    colorize: true,
                    singleLine: true,
                  },
                }
              : undefined,
        },
      }),
      inject: [ConfigService],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            name: 'default',
            ttl: configService.get<number>('throttle.ttl') * 1000, // Convert to milliseconds
            limit: configService.get<number>('throttle.limit'),
          },
        ],
        ignoreUserAgents: [/googlebot/gi, /bingbot/gi],
      }),
      inject: [ConfigService],
    }),

    // Caching
    CacheModule.register({
      isGlobal: true,
      ttl: 300, // 5 minutes
    }),

    // Task scheduling
    ScheduleModule.forRoot(),

    // Database
    DatabaseModule,

    // Queue system
    QueueModule,

    // Feature modules
    UsersModule,
    AuthModule,
    WebSocketModule,
    SchedulerModule,
    HealthModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .forRoutes(UsersController, AuthController, HealthController);
  }
}
