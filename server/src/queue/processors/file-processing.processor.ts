import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { PrismaService } from 'src/database/prisma.service';
import { StorageService } from 'src/modules/storage/storage.service';
import { PaperStatus, AssetType } from 'generated/prisma';
import { AppWebSocketGateway } from 'src/modules/websocket/websocket.gateway';

interface ProcessPaperJobData {
  paperId: string;
  userId: string;
  storageKey: string;
  mimeType: string;
}

interface GenerateAssetJobData {
  paperId: string;
  assetType: AssetType;
  processingOptions?: {
    backgroundColor?: string;
    scale?: number;
    format?: 'png' | 'jpg' | 'webp';
    quality?: number;
    removeBackground?: boolean;
    generateAnimations?: boolean;
  };
  sourceStorageKey: string;
  sourceBucket: string;
}

interface GenerateThumbnailJobData {
  sourceAssetId: string;
  sourceStorageKey: string;
  sourceBucket: string;
}

interface GenerateAnimationJobData {
  sourceAssetId: string;
  sourceStorageKey: string;
  sourceBucket: string;
  paperId: string;
}

@Processor('file-processing')
export class FileProcessingProcessor {
  private readonly logger = new Logger(FileProcessingProcessor.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly websocketGateway: AppWebSocketGateway,
  ) {}

  @Process('process-paper')
  async processPaper(job: Job<ProcessPaperJobData>) {
    const { paperId, userId, storageKey, mimeType } = job.data;

    try {
      this.logger.log(`Processing paper: ${paperId}`);

      // Update status to processing
      await this.prisma.paper.update({
        where: { id: paperId },
        data: { status: PaperStatus.PROCESSING },
      });

      // Notify user via WebSocket
      this.websocketGateway.notifyUser(userId, 'paper-processing', {
        paperId,
        status: 'processing',
        progress: 10,
      });

      // Simulate processing time based on file type
      const processingTime = mimeType === 'application/pdf' ? 5000 : 2000;
      await new Promise((resolve) => setTimeout(resolve, processingTime));

      // Update progress
      this.websocketGateway.notifyUser(userId, 'paper-processing', {
        paperId,
        status: 'processing',
        progress: 50,
      });

      // Generate basic character assets automatically
      await this.generateBasicAssets(paperId, storageKey);

      // Update progress
      this.websocketGateway.notifyUser(userId, 'paper-processing', {
        paperId,
        status: 'processing',
        progress: 90,
      });

      // Mark as processed
      await this.prisma.paper.update({
        where: { id: paperId },
        data: {
          status: PaperStatus.PROCESSED,
          processedAt: new Date(),
        },
      });

      // Notify completion
      this.websocketGateway.notifyUser(userId, 'paper-processed', {
        paperId,
        status: 'completed',
        progress: 100,
      });

      this.logger.log(`Paper processed successfully: ${paperId}`);
    } catch (error) {
      this.logger.error(`Failed to process paper ${paperId}:`, error);

      // Mark as failed
      await this.prisma.paper.update({
        where: { id: paperId },
        data: { status: PaperStatus.FAILED },
      });

      // Notify failure
      this.websocketGateway.notifyUser(userId, 'paper-processing-failed', {
        paperId,
        status: 'failed',
        error: error.message,
      });

      throw error;
    }
  }

  @Process('generate-character-asset')
  async generateCharacterAsset(job: Job<GenerateAssetJobData>) {
    const {
      paperId,
      assetType,
      processingOptions,
      sourceStorageKey,
      sourceBucket,
    } = job.data;

    try {
      this.logger.log(`Generating ${assetType} asset for paper: ${paperId}`);

      // Get paper info
      const paper = await this.prisma.paper.findUnique({
        where: { id: paperId },
      });
      if (!paper) {
        throw new Error('Paper not found');
      }

      // Download source file
      const sourceBuffer = await this.storageService.downloadFile(
        sourceBucket,
        sourceStorageKey,
      );

      // Simulate asset generation based on type
      const generatedAsset = await this.simulateAssetGeneration(
        sourceBuffer,
        assetType,
        processingOptions,
      );

      // Generate filename and storage key
      const filename = `${assetType.toLowerCase()}_${Date.now()}.png`;
      const storageKey = `assets/${paperId}/${assetType.toLowerCase()}/${filename}`;

      // Upload generated asset
      const uploadResult = await this.storageService.uploadFile(
        this.storageService.getAssetsBucket(),
        storageKey,
        generatedAsset,
        'image/png',
        {
          'paper-id': paperId,
          'asset-type': assetType,
          'user-id': paper.userId,
        },
      );

      // Save to database
      const asset = await this.prisma.characterAsset.create({
        data: {
          filename,
          assetType,
          mimeType: 'image/png',
          size: generatedAsset.length,
          storageKey,
          storageBucket: uploadResult.bucket,
          storageUrl: uploadResult.url,
          paperId,
          processingParams: processingOptions,
        },
      });

      // Notify user
      this.websocketGateway.notifyUser(paper.userId, 'asset-generated', {
        paperId,
        assetId: asset.id,
        assetType,
        status: 'completed',
      });

      this.logger.log(`Asset generated successfully: ${asset.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to generate ${assetType} asset for paper ${paperId}:`,
        error,
      );

      // Get paper for user notification
      const paper = await this.prisma.paper.findUnique({
        where: { id: paperId },
      });
      if (paper) {
        this.websocketGateway.notifyUser(
          paper.userId,
          'asset-generation-failed',
          {
            paperId,
            assetType,
            status: 'failed',
            error: error.message,
          },
        );
      }

      throw error;
    }
  }

  @Process('generate-thumbnail')
  async generateThumbnail(job: Job<GenerateThumbnailJobData>) {
    const { sourceAssetId, sourceStorageKey, sourceBucket } = job.data;

    try {
      this.logger.log(`Generating thumbnail for asset: ${sourceAssetId}`);

      // Get source asset
      const sourceAsset = await this.prisma.characterAsset.findUnique({
        where: { id: sourceAssetId },
        include: { paper: true },
      });

      if (!sourceAsset) {
        throw new Error('Source asset not found');
      }

      // Download source file
      const sourceBuffer = await this.storageService.downloadFile(
        sourceBucket,
        sourceStorageKey,
      );

      // Generate thumbnail (simulate)
      const thumbnailBuffer =
        await this.simulateThumbnailGeneration(sourceBuffer);

      // Generate filename and storage key
      const filename = `thumbnail_${Date.now()}.png`;
      const storageKey = `assets/${sourceAsset.paperId}/thumbnails/${filename}`;

      // Upload thumbnail
      const uploadResult = await this.storageService.uploadFile(
        this.storageService.getAssetsBucket(),
        storageKey,
        thumbnailBuffer,
        'image/png',
        {
          'paper-id': sourceAsset.paperId,
          'asset-type': 'THUMBNAIL',
          'source-asset-id': sourceAssetId,
        },
      );

      // Save to database
      const thumbnail = await this.prisma.characterAsset.create({
        data: {
          filename,
          assetType: AssetType.THUMBNAIL,
          mimeType: 'image/png',
          size: thumbnailBuffer.length,
          storageKey,
          storageBucket: uploadResult.bucket,
          storageUrl: uploadResult.url,
          paperId: sourceAsset.paperId,
          processingParams: { sourceAssetId },
        },
      });

      // Notify user
      this.websocketGateway.notifyUser(
        sourceAsset.paper.userId,
        'thumbnail-generated',
        {
          sourceAssetId,
          thumbnailId: thumbnail.id,
          status: 'completed',
        },
      );

      this.logger.log(`Thumbnail generated successfully: ${thumbnail.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to generate thumbnail for asset ${sourceAssetId}:`,
        error,
      );
      throw error;
    }
  }

  @Process('generate-animation-frames')
  async generateAnimationFrames(job: Job<GenerateAnimationJobData>) {
    const { sourceAssetId, sourceStorageKey, sourceBucket, paperId } = job.data;

    try {
      this.logger.log(
        `Generating animation frames for asset: ${sourceAssetId}`,
      );

      // Get source asset and paper
      const [sourceAsset, paper] = await Promise.all([
        this.prisma.characterAsset.findUnique({ where: { id: sourceAssetId } }),
        this.prisma.paper.findUnique({ where: { id: paperId } }),
      ]);

      if (!sourceAsset || !paper) {
        throw new Error('Source asset or paper not found');
      }

      // Download source file
      const sourceBuffer = await this.storageService.downloadFile(
        sourceBucket,
        sourceStorageKey,
      );

      // Generate animation frames (simulate)
      const animationFrames =
        await this.simulateAnimationGeneration(sourceBuffer);

      // Upload each frame
      for (let i = 0; i < animationFrames.length; i++) {
        const frame = animationFrames[i];
        const filename = `animation_frame_${i + 1}_${Date.now()}.png`;
        const storageKey = `assets/${paperId}/animations/${filename}`;

        const uploadResult = await this.storageService.uploadFile(
          this.storageService.getAssetsBucket(),
          storageKey,
          frame,
          'image/png',
          {
            'paper-id': paperId,
            'asset-type': 'ANIMATION',
            'source-asset-id': sourceAssetId,
            'frame-number': (i + 1).toString(),
          },
        );

        // Save frame to database
        await this.prisma.characterAsset.create({
          data: {
            filename,
            assetType: AssetType.ANIMATION,
            mimeType: 'image/png',
            size: frame.length,
            storageKey,
            storageBucket: uploadResult.bucket,
            storageUrl: uploadResult.url,
            paperId,
            processingParams: {
              sourceAssetId,
              frameNumber: i + 1,
              totalFrames: animationFrames.length,
            },
          },
        });
      }

      // Notify user
      this.websocketGateway.notifyUser(paper.userId, 'animation-generated', {
        sourceAssetId,
        paperId,
        frameCount: animationFrames.length,
        status: 'completed',
      });

      this.logger.log(
        `Animation frames generated successfully for asset: ${sourceAssetId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to generate animation frames for asset ${sourceAssetId}:`,
        error,
      );
      throw error;
    }
  }

  private async generateBasicAssets(
    paperId: string,
    sourceStorageKey: string,
  ): Promise<void> {
    // Generate basic sprite asset automatically
    const sourceBuffer = await this.storageService.downloadFile(
      this.storageService.getPapersBucket(),
      sourceStorageKey,
    );

    const spriteBuffer = await this.simulateAssetGeneration(
      sourceBuffer,
      AssetType.SPRITE,
    );

    const filename = `sprite_${Date.now()}.png`;
    const storageKey = `assets/${paperId}/sprites/${filename}`;

    const uploadResult = await this.storageService.uploadFile(
      this.storageService.getAssetsBucket(),
      storageKey,
      spriteBuffer,
      'image/png',
    );

    await this.prisma.characterAsset.create({
      data: {
        filename,
        assetType: AssetType.SPRITE,
        mimeType: 'image/png',
        size: spriteBuffer.length,
        storageKey,
        storageBucket: uploadResult.bucket,
        storageUrl: uploadResult.url,
        paperId,
        processingParams: { autoGenerated: true },
      },
    });
  }

  // Simulation methods (replace with actual image processing)
  private async simulateAssetGeneration(
    _sourceBuffer: Buffer,
    assetType: AssetType,
    _options?: any,
  ): Promise<Buffer> {
    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Return a simple buffer (in real implementation, this would be actual image processing)
    return Buffer.from(`Generated ${assetType} asset`);
  }

  private async simulateThumbnailGeneration(
    _sourceBuffer: Buffer,
  ): Promise<Buffer> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return Buffer.from('Generated thumbnail');
  }

  private async simulateAnimationGeneration(
    _sourceBuffer: Buffer,
  ): Promise<Buffer[]> {
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Return 4 animation frames
    return [
      Buffer.from('Animation frame 1'),
      Buffer.from('Animation frame 2'),
      Buffer.from('Animation frame 3'),
      Buffer.from('Animation frame 4'),
    ];
  }
}
