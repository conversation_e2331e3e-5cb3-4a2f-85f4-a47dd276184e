import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseModule } from 'src/database/database.module';
import { StorageModule } from 'src/modules/storage/storage.module';
import { WebSocketModule } from 'src/modules/websocket/websocket.module';
import { EmailProcessor } from 'src/queue/processors/email.processor';
import { FileProcessingProcessor } from 'src/queue/processors/file-processing.processor';
import { QueueService } from 'src/queue/queue.service';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    StorageModule,
    WebSocketModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('redis.host'),
          port: configService.get<number>('redis.port'),
          password: configService.get<string>('redis.password') || undefined,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'email',
    }),
    BullModule.registerQueue({
      name: 'file-processing',
    }),
  ],
  providers: [EmailProcessor, FileProcessingProcessor, QueueService],
  exports: [QueueService],
})
export class QueueModule {}
