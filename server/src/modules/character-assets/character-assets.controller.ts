import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { CharacterAssetsService } from './character-assets.service';
import { CreateAssetDto, UpdateAssetDto, AssetQueryDto, GenerateAssetDto } from './dto/asset.dto';
import { CharacterAssetResponseDto, AssetListResponseDto, AssetGenerationResponseDto } from './dto/asset-response.dto';

@ApiTags('Character Assets')
@Controller('character-assets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CharacterAssetsController {
  constructor(private readonly characterAssetsService: CharacterAssetsService) {}

  @Post('papers/:paperId/assets')
  @ApiOperation({ summary: 'Create character asset from uploaded file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, type: CharacterAssetResponseDto })
  @UseInterceptors(FileInterceptor('file'))
  async createAsset(
    @Param('paperId') paperId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() createDto: CreateAssetDto,
  ): Promise<CharacterAssetResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    return await this.characterAssetsService.createAsset(paperId, createDto, file);
  }

  @Post('papers/:paperId/generate')
  @ApiOperation({ summary: 'Generate character asset from paper' })
  @ApiResponse({ status: 201, type: AssetGenerationResponseDto })
  async generateAsset(
    @Param('paperId') paperId: string,
    @Body() generateDto: GenerateAssetDto,
  ): Promise<AssetGenerationResponseDto> {
    await this.characterAssetsService.generateAssetsFromPaper({
      paperId,
      assetType: generateDto.assetType,
      processingOptions: generateDto.processingOptions,
    });

    return {
      jobId: `generate_${paperId}_${generateDto.assetType}_${Date.now()}`,
      assetType: generateDto.assetType,
      paperId,
      estimatedTime: 30, // 30 seconds estimate
      status: 'queued',
    };
  }

  @Get('papers/:paperId')
  @ApiOperation({ summary: 'Get all assets for a paper' })
  @ApiResponse({ status: 200, type: AssetListResponseDto })
  async getPaperAssets(
    @Param('paperId') paperId: string,
    @Query() query: AssetQueryDto,
  ): Promise<AssetListResponseDto> {
    return await this.characterAssetsService.getPaperAssets(paperId, query);
  }

  @Get('user/assets')
  @ApiOperation({ summary: 'Get all assets for current user' })
  @ApiResponse({ status: 200, type: AssetListResponseDto })
  async getUserAssets(
    @Request() req: any,
    @Query() query: AssetQueryDto,
  ): Promise<AssetListResponseDto> {
    const userId = req.user.userId;
    return await this.characterAssetsService.getUserAssets(userId, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get character asset details' })
  @ApiResponse({ status: 200, type: CharacterAssetResponseDto })
  async getAsset(@Param('id') id: string): Promise<CharacterAssetResponseDto> {
    return await this.characterAssetsService.getAsset(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update character asset' })
  @ApiResponse({ status: 200, type: CharacterAssetResponseDto })
  async updateAsset(
    @Param('id') id: string,
    @Body() updateDto: UpdateAssetDto,
  ): Promise<CharacterAssetResponseDto> {
    return await this.characterAssetsService.updateAsset(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete character asset' })
  @ApiResponse({ status: 200, description: 'Asset deleted successfully' })
  async deleteAsset(@Param('id') id: string): Promise<{ message: string }> {
    await this.characterAssetsService.deleteAsset(id);
    return { message: 'Character asset deleted successfully' };
  }

  @Post(':id/thumbnail')
  @ApiOperation({ summary: 'Generate thumbnail for asset' })
  @ApiResponse({ status: 201, type: CharacterAssetResponseDto })
  async generateThumbnail(@Param('id') id: string): Promise<CharacterAssetResponseDto> {
    return await this.characterAssetsService.generateThumbnail(id);
  }

  @Post(':id/animation-frames')
  @ApiOperation({ summary: 'Generate animation frames from sprite asset' })
  @ApiResponse({ status: 201, description: 'Animation frame generation queued' })
  async generateAnimationFrames(@Param('id') id: string): Promise<{ message: string; jobId: string }> {
    await this.characterAssetsService.generateAnimationFrames(id);
    return {
      message: 'Animation frame generation queued',
      jobId: `animation_${id}_${Date.now()}`,
    };
  }

  @Get(':id/download')
  @ApiOperation({ summary: 'Get download URL for asset' })
  @ApiResponse({ status: 200, description: 'Returns pre-signed download URL' })
  async getDownloadUrl(@Param('id') id: string): Promise<{ url: string; expiresIn: number }> {
    const asset = await this.characterAssetsService.getAsset(id);
    
    // This would typically use the storage service to generate a pre-signed URL
    // For now, return the existing storage URL
    return {
      url: asset.storageUrl || '',
      expiresIn: 3600, // 1 hour
    };
  }
}
