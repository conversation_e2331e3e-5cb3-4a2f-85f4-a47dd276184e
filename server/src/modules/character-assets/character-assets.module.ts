import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/database/database.module';
import { StorageModule } from 'src/modules/storage/storage.module';
import { QueueModule } from 'src/queue/queue.module';
import { CharacterAssetsController } from './character-assets.controller';
import { CharacterAssetsService } from './character-assets.service';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    StorageModule,
    QueueModule,
  ],
  controllers: [CharacterAssetsController],
  providers: [CharacterAssetsService],
  exports: [CharacterAssetsService],
})
export class CharacterAssetsModule {}
