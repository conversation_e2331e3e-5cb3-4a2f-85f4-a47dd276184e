import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from 'src/database/prisma.service';
import { StorageService } from 'src/modules/storage/storage.service';
import { QueueService } from 'src/queue/queue.service';
import { AssetType } from '@prisma/client';
import { CreateAssetDto, UpdateAssetDto, AssetQueryDto } from './dto/asset.dto';
import { CharacterAssetResponseDto, AssetListResponseDto } from './dto/asset-response.dto';
import { FileValidationUtil } from '../papers/utils/file-validation.util';
import { v4 as uuidv4 } from 'uuid';

export interface AssetGenerationParams {
  paperId: string;
  assetType: AssetType;
  processingOptions?: {
    backgroundColor?: string;
    scale?: number;
    format?: 'png' | 'jpg' | 'webp';
    quality?: number;
    removeBackground?: boolean;
    generateAnimations?: boolean;
  };
}

@Injectable()
export class CharacterAssetsService {
  private readonly logger = new Logger(CharacterAssetsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly queueService: QueueService,
  ) {}

  async createAsset(
    paperId: string,
    createDto: CreateAssetDto,
    file: Express.Multer.File,
  ): Promise<CharacterAssetResponseDto> {
    // Validate paper exists
    const paper = await this.prisma.paper.findUnique({ where: { id: paperId } });
    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    // Validate file
    FileValidationUtil.validateImageFile(file);

    // Generate safe filename
    const filename = FileValidationUtil.generateSafeFilename(
      file.originalname,
      `${createDto.assetType.toLowerCase()}_asset`
    );
    const storageKey = `assets/${paperId}/${createDto.assetType.toLowerCase()}/${filename}`;

    try {
      // Upload to MinIO
      const uploadResult = await this.storageService.uploadFile(
        this.storageService.getAssetsBucket(),
        storageKey,
        file.buffer,
        file.mimetype,
        {
          'paper-id': paperId,
          'asset-type': createDto.assetType,
          'user-id': paper.userId,
        },
      );

      // Save to database
      const asset = await this.prisma.characterAsset.create({
        data: {
          filename,
          assetType: createDto.assetType,
          mimeType: file.mimetype,
          size: file.size,
          storageKey,
          storageBucket: uploadResult.bucket,
          storageUrl: uploadResult.url,
          paperId,
          processingParams: createDto.processingParams,
        },
      });

      this.logger.log(`Character asset created: ${asset.id} for paper ${paperId}`);
      return this.mapToResponseDto(asset);
    } catch (error) {
      this.logger.error(`Failed to create character asset for paper ${paperId}:`, error);
      throw new BadRequestException('Failed to create character asset');
    }
  }

  async generateAssetsFromPaper(params: AssetGenerationParams): Promise<void> {
    const { paperId, assetType, processingOptions } = params;

    // Validate paper exists
    const paper = await this.prisma.paper.findUnique({ where: { id: paperId } });
    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    // Queue asset generation job
    await this.queueService.addJob('file-processing', 'generate-character-asset', {
      paperId,
      assetType,
      processingOptions,
      sourceStorageKey: paper.storageKey,
      sourceBucket: paper.storageBucket,
    });

    this.logger.log(`Queued asset generation: ${assetType} for paper ${paperId}`);
  }

  async getAsset(id: string): Promise<CharacterAssetResponseDto> {
    const asset = await this.prisma.characterAsset.findUnique({
      where: { id },
      include: {
        paper: {
          select: {
            id: true,
            originalName: true,
            userId: true,
          },
        },
      },
    });

    if (!asset) {
      throw new NotFoundException('Character asset not found');
    }

    return this.mapToResponseDto(asset);
  }

  async getPaperAssets(paperId: string, query: AssetQueryDto): Promise<AssetListResponseDto> {
    const { assetType, page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const where = {
      paperId,
      ...(assetType && { assetType }),
    };

    const [assets, total] = await Promise.all([
      this.prisma.characterAsset.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.characterAsset.count({ where }),
    ]);

    return {
      assets: assets.map(asset => this.mapToResponseDto(asset)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getUserAssets(userId: string, query: AssetQueryDto): Promise<AssetListResponseDto> {
    const { assetType, page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const where = {
      paper: { userId },
      ...(assetType && { assetType }),
    };

    const [assets, total] = await Promise.all([
      this.prisma.characterAsset.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          paper: {
            select: {
              id: true,
              originalName: true,
            },
          },
        },
      }),
      this.prisma.characterAsset.count({ where }),
    ]);

    return {
      assets: assets.map(asset => this.mapToResponseDto(asset)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateAsset(id: string, updateDto: UpdateAssetDto): Promise<CharacterAssetResponseDto> {
    const asset = await this.prisma.characterAsset.findUnique({ where: { id } });
    if (!asset) {
      throw new NotFoundException('Character asset not found');
    }

    const updatedAsset = await this.prisma.characterAsset.update({
      where: { id },
      data: {
        processingParams: updateDto.processingParams
          ? { ...asset.processingParams, ...updateDto.processingParams }
          : asset.processingParams,
      },
    });

    this.logger.log(`Character asset updated: ${id}`);
    return this.mapToResponseDto(updatedAsset);
  }

  async deleteAsset(id: string): Promise<void> {
    const asset = await this.prisma.characterAsset.findUnique({ where: { id } });
    if (!asset) {
      throw new NotFoundException('Character asset not found');
    }

    try {
      // Delete from storage
      await this.storageService.deleteFile(asset.storageBucket, asset.storageKey);

      // Delete from database
      await this.prisma.characterAsset.delete({ where: { id } });

      this.logger.log(`Character asset deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete character asset ${id}:`, error);
      throw new BadRequestException('Failed to delete character asset');
    }
  }

  async generateThumbnail(assetId: string): Promise<CharacterAssetResponseDto> {
    const asset = await this.prisma.characterAsset.findUnique({ where: { id: assetId } });
    if (!asset) {
      throw new NotFoundException('Character asset not found');
    }

    // Queue thumbnail generation
    await this.queueService.addJob('file-processing', 'generate-thumbnail', {
      sourceAssetId: assetId,
      sourceStorageKey: asset.storageKey,
      sourceBucket: asset.storageBucket,
    });

    this.logger.log(`Queued thumbnail generation for asset: ${assetId}`);
    return this.mapToResponseDto(asset);
  }

  async generateAnimationFrames(assetId: string): Promise<void> {
    const asset = await this.prisma.characterAsset.findUnique({ where: { id: assetId } });
    if (!asset) {
      throw new NotFoundException('Character asset not found');
    }

    if (asset.assetType !== AssetType.SPRITE) {
      throw new BadRequestException('Animation frames can only be generated from sprite assets');
    }

    // Queue animation generation
    await this.queueService.addJob('file-processing', 'generate-animation-frames', {
      sourceAssetId: assetId,
      sourceStorageKey: asset.storageKey,
      sourceBucket: asset.storageBucket,
      paperId: asset.paperId,
    });

    this.logger.log(`Queued animation frame generation for asset: ${assetId}`);
  }

  private mapToResponseDto(asset: any): CharacterAssetResponseDto {
    return {
      id: asset.id,
      filename: asset.filename,
      assetType: asset.assetType,
      mimeType: asset.mimeType,
      size: asset.size,
      storageKey: asset.storageKey,
      storageBucket: asset.storageBucket,
      storageUrl: asset.storageUrl,
      paperId: asset.paperId,
      processingParams: asset.processingParams,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
      paper: asset.paper,
    };
  }
}
