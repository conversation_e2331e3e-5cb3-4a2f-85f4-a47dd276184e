import { ApiProperty } from '@nestjs/swagger';
import { AssetType } from '@prisma/client';

export class CharacterAssetResponseDto {
  @ApiProperty({ description: 'Asset ID' })
  id: string;

  @ApiProperty({ description: 'Filename in storage' })
  filename: string;

  @ApiProperty({ enum: AssetType, description: 'Asset type' })
  assetType: AssetType;

  @ApiProperty({ description: 'MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'File size in bytes' })
  size: number;

  @ApiProperty({ description: 'Storage key' })
  storageKey: string;

  @ApiProperty({ description: 'Storage bucket' })
  storageBucket: string;

  @ApiProperty({ description: 'Storage URL', required: false })
  storageUrl?: string;

  @ApiProperty({ description: 'Source paper ID' })
  paperId: string;

  @ApiProperty({ description: 'Processing parameters', required: false })
  processingParams?: any;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Paper information', required: false })
  paper?: {
    id: string;
    originalName: string;
    userId?: string;
  };
}

export class AssetListResponseDto {
  @ApiProperty({ type: [CharacterAssetResponseDto], description: 'List of character assets' })
  assets: CharacterAssetResponseDto[];

  @ApiProperty({ description: 'Total number of assets' })
  total: number;

  @ApiProperty({ description: 'Current page' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total pages' })
  totalPages: number;
}

export class AssetGenerationResponseDto {
  @ApiProperty({ description: 'Job ID for tracking generation progress' })
  jobId: string;

  @ApiProperty({ description: 'Asset type being generated' })
  assetType: AssetType;

  @ApiProperty({ description: 'Source paper ID' })
  paperId: string;

  @ApiProperty({ description: 'Estimated completion time in seconds' })
  estimatedTime: number;

  @ApiProperty({ description: 'Generation status' })
  status: 'queued' | 'processing' | 'completed' | 'failed';
}
