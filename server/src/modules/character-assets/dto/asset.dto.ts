import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsObject, IsString, IsNumber, IsBoolean } from 'class-validator';
import { AssetType } from '@prisma/client';

export class CreateAssetDto {
  @ApiProperty({ enum: AssetType, description: 'Type of character asset' })
  @IsEnum(AssetType)
  assetType: AssetType;

  @ApiProperty({ description: 'Processing parameters', required: false })
  @IsOptional()
  @IsObject()
  processingParams?: {
    backgroundColor?: string;
    scale?: number;
    format?: 'png' | 'jpg' | 'webp';
    quality?: number;
    removeBackground?: boolean;
    generateAnimations?: boolean;
  };
}

export class UpdateAssetDto {
  @ApiProperty({ description: 'Processing parameters', required: false })
  @IsOptional()
  @IsObject()
  processingParams?: {
    backgroundColor?: string;
    scale?: number;
    format?: 'png' | 'jpg' | 'webp';
    quality?: number;
    removeBackground?: boolean;
    generateAnimations?: boolean;
  };
}

export class AssetQueryDto {
  @ApiProperty({ enum: AssetType, description: 'Filter by asset type', required: false })
  @IsOptional()
  @IsEnum(AssetType)
  assetType?: AssetType;

  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', required: false, default: 10 })
  @IsOptional()
  @IsNumber()
  limit?: number = 10;
}

export class GenerateAssetDto {
  @ApiProperty({ enum: AssetType, description: 'Type of asset to generate' })
  @IsEnum(AssetType)
  assetType: AssetType;

  @ApiProperty({ description: 'Processing options', required: false })
  @IsOptional()
  @IsObject()
  processingOptions?: {
    backgroundColor?: string;
    scale?: number;
    format?: 'png' | 'jpg' | 'webp';
    quality?: number;
    removeBackground?: boolean;
    generateAnimations?: boolean;
  };
}

export class ProcessingOptionsDto {
  @ApiProperty({ description: 'Background color (hex)', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;

  @ApiProperty({ description: 'Scale factor', required: false })
  @IsOptional()
  @IsNumber()
  scale?: number;

  @ApiProperty({ description: 'Output format', required: false })
  @IsOptional()
  @IsString()
  format?: 'png' | 'jpg' | 'webp';

  @ApiProperty({ description: 'Quality (1-100)', required: false })
  @IsOptional()
  @IsNumber()
  quality?: number;

  @ApiProperty({ description: 'Remove background', required: false })
  @IsOptional()
  @IsBoolean()
  removeBackground?: boolean;

  @ApiProperty({ description: 'Generate animations', required: false })
  @IsOptional()
  @IsBoolean()
  generateAnimations?: boolean;
}
