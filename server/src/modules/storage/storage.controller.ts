import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  NotFoundException,
  UseGuards,
  Res,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { StorageService } from './storage.service';
import { UploadFileDto, GenerateUploadUrlDto, GenerateDownloadUrlDto } from './dto/upload.dto';
import { UploadResponseDto, FileMetadataResponseDto, FileListResponseDto, UrlResponseDto } from './dto/response.dto';

@ApiTags('Storage')
@Controller('storage')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Post('upload/:bucket')
  @ApiOperation({ summary: 'Upload file to storage bucket' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, type: UploadResponseDto })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Param('bucket') bucket: string,
    @UploadedFile() file: any,
    @Body() uploadDto: UploadFileDto,
  ): Promise<UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    const key = uploadDto.key || `${Date.now()}-${file.originalname}`;

    return await this.storageService.uploadFile(
      bucketName,
      key,
      file.buffer,
      uploadDto.contentType || file.mimetype,
      uploadDto.metadata,
    );
  }

  @Get('download/:bucket/:key(*)')
  @ApiOperation({ summary: 'Download file from storage bucket' })
  async downloadFile(
    @Param('bucket') bucket: string,
    @Param('key') key: string,
    @Res() res: Response,
  ): Promise<void> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    
    try {
      const fileExists = await this.storageService.fileExists(bucketName, key);
      if (!fileExists) {
        throw new NotFoundException('File not found');
      }

      const fileInfo = await this.storageService.getFileInfo(bucketName, key);
      const stream = await this.storageService.getFileStream(bucketName, key);

      res.setHeader('Content-Type', fileInfo.contentType || 'application/octet-stream');
      res.setHeader('Content-Length', fileInfo.size);
      res.setHeader('Content-Disposition', `attachment; filename="${key}"`);

      stream.pipe(res);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to download file');
    }
  }

  @Delete(':bucket/:key(*)')
  @ApiOperation({ summary: 'Delete file from storage bucket' })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  async deleteFile(
    @Param('bucket') bucket: string,
    @Param('key') key: string,
  ): Promise<{ message: string }> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    
    const fileExists = await this.storageService.fileExists(bucketName, key);
    if (!fileExists) {
      throw new NotFoundException('File not found');
    }

    await this.storageService.deleteFile(bucketName, key);
    return { message: 'File deleted successfully' };
  }

  @Get('list/:bucket')
  @ApiOperation({ summary: 'List files in storage bucket' })
  @ApiResponse({ status: 200, type: FileListResponseDto })
  async listFiles(
    @Param('bucket') bucket: string,
    @Query('prefix') prefix?: string,
  ): Promise<FileListResponseDto> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    const files = await this.storageService.listFiles(bucketName, prefix);

    return {
      files,
      total: files.length,
    };
  }

  @Get('info/:bucket/:key(*)')
  @ApiOperation({ summary: 'Get file information' })
  @ApiResponse({ status: 200, type: FileMetadataResponseDto })
  async getFileInfo(
    @Param('bucket') bucket: string,
    @Param('key') key: string,
  ): Promise<FileMetadataResponseDto> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    
    const fileExists = await this.storageService.fileExists(bucketName, key);
    if (!fileExists) {
      throw new NotFoundException('File not found');
    }

    return await this.storageService.getFileInfo(bucketName, key);
  }

  @Post('url/upload/:bucket')
  @ApiOperation({ summary: 'Generate pre-signed upload URL' })
  @ApiResponse({ status: 201, type: UrlResponseDto })
  async generateUploadUrl(
    @Param('bucket') bucket: string,
    @Body() generateUrlDto: GenerateUploadUrlDto,
  ): Promise<UrlResponseDto> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    const expiry = generateUrlDto.expiry || 3600; // 1 hour default
    
    const url = await this.storageService.getUploadUrl(bucketName, generateUrlDto.key, expiry);

    return {
      url,
      expiresIn: expiry,
    };
  }

  @Post('url/download/:bucket')
  @ApiOperation({ summary: 'Generate pre-signed download URL' })
  @ApiResponse({ status: 201, type: UrlResponseDto })
  async generateDownloadUrl(
    @Param('bucket') bucket: string,
    @Body() generateUrlDto: GenerateDownloadUrlDto,
  ): Promise<UrlResponseDto> {
    const validBuckets = ['papers', 'assets', 'temp'];
    if (!validBuckets.includes(bucket)) {
      throw new BadRequestException('Invalid bucket name');
    }

    const bucketName = this.getBucketName(bucket);
    const expiry = generateUrlDto.expiry || 604800; // 7 days default
    
    const url = await this.storageService.getFileUrl(bucketName, generateUrlDto.key, expiry);

    return {
      url,
      expiresIn: expiry,
    };
  }

  private getBucketName(bucket: string): string {
    switch (bucket) {
      case 'papers':
        return this.storageService.getPapersBucket();
      case 'assets':
        return this.storageService.getAssetsBucket();
      case 'temp':
        return this.storageService.getTempBucket();
      default:
        throw new BadRequestException('Invalid bucket name');
    }
  }
}
