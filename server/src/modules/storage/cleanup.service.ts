import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'src/database/prisma.service';
import { StorageService } from './storage.service';
import { ConfigService } from '@nestjs/config';
import { PaperStatus } from 'generated/prisma';

@Injectable()
export class CleanupService {
  private readonly logger = new Logger(CleanupService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly configService: ConfigService,
  ) {}

  // Run cleanup every day at 2 AM
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async runDailyCleanup() {
    this.logger.log('Starting daily cleanup process');
    
    try {
      await Promise.all([
        this.cleanupTempFiles(),
        this.cleanupFailedUploads(),
        this.cleanupOldProcessedFiles(),
        this.cleanupOrphanedAssets(),
      ]);
      
      this.logger.log('Daily cleanup completed successfully');
    } catch (error) {
      this.logger.error('Daily cleanup failed:', error);
    }
  }

  // Run temp file cleanup every hour
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupTempFiles() {
    this.logger.log('Cleaning up temporary files');
    
    try {
      // Get temp files older than 1 hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      
      // List files in temp bucket
      const tempFiles = await this.storageService.listFiles(
        this.storageService.getTempBucket()
      );
      
      const filesToDelete = tempFiles.filter(
        file => file.lastModified < oneHourAgo
      );
      
      if (filesToDelete.length > 0) {
        const keys = filesToDelete.map(file => file.name);
        await this.storageService.deleteFiles(
          this.storageService.getTempBucket(),
          keys
        );
        
        this.logger.log(`Deleted ${filesToDelete.length} temporary files`);
      }
    } catch (error) {
      this.logger.error('Failed to cleanup temp files:', error);
    }
  }

  async cleanupFailedUploads() {
    this.logger.log('Cleaning up failed uploads');
    
    try {
      // Get papers that failed more than 24 hours ago
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const failedPapers = await this.prisma.paper.findMany({
        where: {
          status: PaperStatus.FAILED,
          updatedAt: {
            lt: oneDayAgo,
          },
        },
        include: {
          characterAssets: true,
        },
      });
      
      for (const paper of failedPapers) {
        try {
          // Delete from storage
          await this.storageService.deleteFile(paper.storageBucket, paper.storageKey);
          
          // Delete associated assets
          for (const asset of paper.characterAssets) {
            await this.storageService.deleteFile(asset.storageBucket, asset.storageKey);
          }
          
          // Delete from database
          await this.prisma.paper.delete({ where: { id: paper.id } });
          
          this.logger.log(`Cleaned up failed paper: ${paper.id}`);
        } catch (error) {
          this.logger.error(`Failed to cleanup paper ${paper.id}:`, error);
        }
      }
      
      this.logger.log(`Cleaned up ${failedPapers.length} failed uploads`);
    } catch (error) {
      this.logger.error('Failed to cleanup failed uploads:', error);
    }
  }

  async cleanupOldProcessedFiles() {
    this.logger.log('Cleaning up old processed files');
    
    try {
      // Get retention period from config (default 90 days)
      const retentionDays = this.configService.get<number>('storage.retentionDays') || 90;
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
      
      const oldPapers = await this.prisma.paper.findMany({
        where: {
          status: PaperStatus.PROCESSED,
          processedAt: {
            lt: cutoffDate,
          },
        },
        include: {
          characterAssets: true,
        },
      });
      
      for (const paper of oldPapers) {
        try {
          // Delete from storage
          await this.storageService.deleteFile(paper.storageBucket, paper.storageKey);
          
          // Delete associated assets
          for (const asset of paper.characterAssets) {
            await this.storageService.deleteFile(asset.storageBucket, asset.storageKey);
          }
          
          // Delete from database
          await this.prisma.paper.delete({ where: { id: paper.id } });
          
          this.logger.log(`Cleaned up old paper: ${paper.id}`);
        } catch (error) {
          this.logger.error(`Failed to cleanup old paper ${paper.id}:`, error);
        }
      }
      
      this.logger.log(`Cleaned up ${oldPapers.length} old processed files`);
    } catch (error) {
      this.logger.error('Failed to cleanup old processed files:', error);
    }
  }

  async cleanupOrphanedAssets() {
    this.logger.log('Cleaning up orphaned assets');
    
    try {
      // Find assets in storage that don't exist in database
      const [paperFiles, assetFiles] = await Promise.all([
        this.storageService.listFiles(this.storageService.getPapersBucket()),
        this.storageService.listFiles(this.storageService.getAssetsBucket()),
      ]);
      
      // Get all storage keys from database
      const [dbPapers, dbAssets] = await Promise.all([
        this.prisma.paper.findMany({ select: { storageKey: true } }),
        this.prisma.characterAsset.findMany({ select: { storageKey: true } }),
      ]);
      
      const dbPaperKeys = new Set(dbPapers.map(p => p.storageKey));
      const dbAssetKeys = new Set(dbAssets.map(a => a.storageKey));
      
      // Find orphaned paper files
      const orphanedPaperFiles = paperFiles.filter(
        file => !dbPaperKeys.has(file.name)
      );
      
      // Find orphaned asset files
      const orphanedAssetFiles = assetFiles.filter(
        file => !dbAssetKeys.has(file.name)
      );
      
      // Delete orphaned files
      if (orphanedPaperFiles.length > 0) {
        const keys = orphanedPaperFiles.map(file => file.name);
        await this.storageService.deleteFiles(
          this.storageService.getPapersBucket(),
          keys
        );
        this.logger.log(`Deleted ${orphanedPaperFiles.length} orphaned paper files`);
      }
      
      if (orphanedAssetFiles.length > 0) {
        const keys = orphanedAssetFiles.map(file => file.name);
        await this.storageService.deleteFiles(
          this.storageService.getAssetsBucket(),
          keys
        );
        this.logger.log(`Deleted ${orphanedAssetFiles.length} orphaned asset files`);
      }
      
    } catch (error) {
      this.logger.error('Failed to cleanup orphaned assets:', error);
    }
  }

  // Manual cleanup methods
  async cleanupUserData(userId: string) {
    this.logger.log(`Cleaning up data for user: ${userId}`);
    
    try {
      const userPapers = await this.prisma.paper.findMany({
        where: { userId },
        include: { characterAssets: true },
      });
      
      for (const paper of userPapers) {
        // Delete from storage
        await this.storageService.deleteFile(paper.storageBucket, paper.storageKey);
        
        // Delete associated assets
        for (const asset of paper.characterAssets) {
          await this.storageService.deleteFile(asset.storageBucket, asset.storageKey);
        }
      }
      
      // Delete from database (cascade will handle assets)
      await this.prisma.paper.deleteMany({ where: { userId } });
      
      this.logger.log(`Cleaned up data for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup user data for ${userId}:`, error);
      throw error;
    }
  }

  async getStorageStats() {
    try {
      const [paperFiles, assetFiles, tempFiles] = await Promise.all([
        this.storageService.listFiles(this.storageService.getPapersBucket()),
        this.storageService.listFiles(this.storageService.getAssetsBucket()),
        this.storageService.listFiles(this.storageService.getTempBucket()),
      ]);
      
      const paperStats = {
        count: paperFiles.length,
        totalSize: paperFiles.reduce((sum, file) => sum + file.size, 0),
      };
      
      const assetStats = {
        count: assetFiles.length,
        totalSize: assetFiles.reduce((sum, file) => sum + file.size, 0),
      };
      
      const tempStats = {
        count: tempFiles.length,
        totalSize: tempFiles.reduce((sum, file) => sum + file.size, 0),
      };
      
      return {
        papers: paperStats,
        assets: assetStats,
        temp: tempStats,
        total: {
          count: paperStats.count + assetStats.count + tempStats.count,
          totalSize: paperStats.totalSize + assetStats.totalSize + tempStats.totalSize,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get storage stats:', error);
      throw error;
    }
  }
}
