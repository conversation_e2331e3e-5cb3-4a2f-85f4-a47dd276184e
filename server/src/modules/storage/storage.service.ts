import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { Readable } from 'stream';

export interface UploadResult {
  bucket: string;
  key: string;
  etag: string;
  size: number;
  url: string;
}

export interface FileMetadata {
  name: string;
  size: number;
  etag: string;
  lastModified: Date;
  contentType?: string;
}

@Injectable()
export class StorageService implements OnModuleInit {
  private readonly logger = new Logger(StorageService.name);
  private minioClient: Minio.Client;
  private readonly buckets: {
    papers: string;
    assets: string;
    temp: string;
  };

  constructor(private readonly configService: ConfigService) {
    const minioConfig = this.configService.get('minio');
    
    this.minioClient = new Minio.Client({
      endPoint: minioConfig.endPoint,
      port: minioConfig.port,
      useSSL: minioConfig.useSSL,
      accessKey: minioConfig.accessKey,
      secretKey: minioConfig.secretKey,
    });

    this.buckets = minioConfig.buckets;
  }

  async onModuleInit() {
    await this.initializeBuckets();
  }

  private async initializeBuckets() {
    const bucketNames = Object.values(this.buckets);
    
    for (const bucketName of bucketNames) {
      try {
        const exists = await this.minioClient.bucketExists(bucketName);
        if (!exists) {
          await this.minioClient.makeBucket(bucketName);
          this.logger.log(`Created bucket: ${bucketName}`);
        } else {
          this.logger.log(`Bucket already exists: ${bucketName}`);
        }
      } catch (error) {
        this.logger.error(`Failed to initialize bucket ${bucketName}:`, error);
        throw error;
      }
    }
  }

  async uploadFile(
    bucket: string,
    key: string,
    buffer: Buffer,
    contentType?: string,
    metadata?: Record<string, string>
  ): Promise<UploadResult> {
    try {
      const stream = Readable.from(buffer);
      const uploadInfo = await this.minioClient.putObject(
        bucket,
        key,
        stream,
        buffer.length,
        {
          'Content-Type': contentType || 'application/octet-stream',
          ...metadata,
        }
      );

      const url = await this.getFileUrl(bucket, key);

      return {
        bucket,
        key,
        etag: uploadInfo.etag,
        size: buffer.length,
        url,
      };
    } catch (error) {
      this.logger.error(`Failed to upload file ${key} to bucket ${bucket}:`, error);
      throw error;
    }
  }

  async uploadStream(
    bucket: string,
    key: string,
    stream: Readable,
    size: number,
    contentType?: string,
    metadata?: Record<string, string>
  ): Promise<UploadResult> {
    try {
      const uploadInfo = await this.minioClient.putObject(
        bucket,
        key,
        stream,
        size,
        {
          'Content-Type': contentType || 'application/octet-stream',
          ...metadata,
        }
      );

      const url = await this.getFileUrl(bucket, key);

      return {
        bucket,
        key,
        etag: uploadInfo.etag,
        size,
        url,
      };
    } catch (error) {
      this.logger.error(`Failed to upload stream ${key} to bucket ${bucket}:`, error);
      throw error;
    }
  }

  async downloadFile(bucket: string, key: string): Promise<Buffer> {
    try {
      const stream = await this.minioClient.getObject(bucket, key);
      const chunks: Buffer[] = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Failed to download file ${key} from bucket ${bucket}:`, error);
      throw error;
    }
  }

  async getFileStream(bucket: string, key: string): Promise<Readable> {
    try {
      return await this.minioClient.getObject(bucket, key);
    } catch (error) {
      this.logger.error(`Failed to get file stream ${key} from bucket ${bucket}:`, error);
      throw error;
    }
  }

  async deleteFile(bucket: string, key: string): Promise<void> {
    try {
      await this.minioClient.removeObject(bucket, key);
      this.logger.log(`Deleted file ${key} from bucket ${bucket}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key} from bucket ${bucket}:`, error);
      throw error;
    }
  }

  async deleteFiles(bucket: string, keys: string[]): Promise<void> {
    try {
      await this.minioClient.removeObjects(bucket, keys);
      this.logger.log(`Deleted ${keys.length} files from bucket ${bucket}`);
    } catch (error) {
      this.logger.error(`Failed to delete files from bucket ${bucket}:`, error);
      throw error;
    }
  }

  async listFiles(bucket: string, prefix?: string): Promise<FileMetadata[]> {
    try {
      const objects: FileMetadata[] = [];
      const stream = this.minioClient.listObjects(bucket, prefix, true);

      return new Promise((resolve, reject) => {
        stream.on('data', (obj) => {
          objects.push({
            name: obj.name,
            size: obj.size,
            etag: obj.etag,
            lastModified: obj.lastModified,
          });
        });
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Failed to list files in bucket ${bucket}:`, error);
      throw error;
    }
  }

  async fileExists(bucket: string, key: string): Promise<boolean> {
    try {
      await this.minioClient.statObject(bucket, key);
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      this.logger.error(`Failed to check if file exists ${key} in bucket ${bucket}:`, error);
      throw error;
    }
  }

  async getFileInfo(bucket: string, key: string): Promise<FileMetadata> {
    try {
      const stat = await this.minioClient.statObject(bucket, key);
      return {
        name: key,
        size: stat.size,
        etag: stat.etag,
        lastModified: stat.lastModified,
        contentType: stat.metaData['content-type'],
      };
    } catch (error) {
      this.logger.error(`Failed to get file info ${key} from bucket ${bucket}:`, error);
      throw error;
    }
  }

  async getFileUrl(bucket: string, key: string, expiry: number = 7 * 24 * 60 * 60): Promise<string> {
    try {
      return await this.minioClient.presignedGetObject(bucket, key, expiry);
    } catch (error) {
      this.logger.error(`Failed to generate URL for ${key} in bucket ${bucket}:`, error);
      throw error;
    }
  }

  async getUploadUrl(bucket: string, key: string, expiry: number = 60 * 60): Promise<string> {
    try {
      return await this.minioClient.presignedPutObject(bucket, key, expiry);
    } catch (error) {
      this.logger.error(`Failed to generate upload URL for ${key} in bucket ${bucket}:`, error);
      throw error;
    }
  }

  // Bucket-specific helper methods
  getPapersBucket(): string {
    return this.buckets.papers;
  }

  getAssetsBucket(): string {
    return this.buckets.assets;
  }

  getTempBucket(): string {
    return this.buckets.temp;
  }
}
