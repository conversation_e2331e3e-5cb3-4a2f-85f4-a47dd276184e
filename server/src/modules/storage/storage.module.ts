import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/database/database.module';
import { StorageService } from './storage.service';
import { StorageController } from './storage.controller';
import { CleanupService } from './cleanup.service';

@Module({
  imports: [ConfigModule, DatabaseModule],
  controllers: [StorageController],
  providers: [StorageService, CleanupService],
  exports: [StorageService, CleanupService],
})
export class StorageModule {}
