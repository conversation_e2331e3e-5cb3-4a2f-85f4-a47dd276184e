import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsObject } from 'class-validator';

export class UploadFileDto {
  @ApiProperty({ description: 'File key/name in storage' })
  @IsString()
  key: string;

  @ApiProperty({ description: 'Content type of the file', required: false })
  @IsOptional()
  @IsString()
  contentType?: string;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;
}

export class GenerateUploadUrlDto {
  @ApiProperty({ description: 'File key/name in storage' })
  @IsString()
  key: string;

  @ApiProperty({ description: 'URL expiry time in seconds', required: false, default: 3600 })
  @IsOptional()
  expiry?: number;
}

export class GenerateDownloadUrlDto {
  @ApiProperty({ description: 'File key/name in storage' })
  @IsString()
  key: string;

  @ApiProperty({ description: 'URL expiry time in seconds', required: false, default: 604800 })
  @IsOptional()
  expiry?: number;
}
