import { ApiProperty } from '@nestjs/swagger';

export class UploadResponseDto {
  @ApiProperty({ description: 'Storage bucket name' })
  bucket: string;

  @ApiProperty({ description: 'File key/name in storage' })
  key: string;

  @ApiProperty({ description: 'File ETag' })
  etag: string;

  @ApiProperty({ description: 'File size in bytes' })
  size: number;

  @ApiProperty({ description: 'File access URL' })
  url: string;
}

export class FileMetadataResponseDto {
  @ApiProperty({ description: 'File name' })
  name: string;

  @ApiProperty({ description: 'File size in bytes' })
  size: number;

  @ApiProperty({ description: 'File ETag' })
  etag: string;

  @ApiProperty({ description: 'Last modified date' })
  lastModified: Date;

  @ApiProperty({ description: 'Content type', required: false })
  contentType?: string;
}

export class FileListResponseDto {
  @ApiProperty({ type: [FileMetadataResponseDto], description: 'List of files' })
  files: FileMetadataResponseDto[];

  @ApiProperty({ description: 'Total number of files' })
  total: number;
}

export class UrlResponseDto {
  @ApiProperty({ description: 'Pre-signed URL' })
  url: string;

  @ApiProperty({ description: 'URL expiry time in seconds' })
  expiresIn: number;
}
