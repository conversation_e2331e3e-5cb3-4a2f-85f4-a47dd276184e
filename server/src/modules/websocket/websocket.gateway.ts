import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';

@WebSocketGateway({ cors: { origin: '*' } })
export class AppWebSocketGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private logger: Logger = new Logger('WebSocketGateway');

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.join(data.room);
    this.logger.log(`Client ${client.id} joined room: ${data.room}`);
    client.emit('joined-room', { room: data.room });
  }

  @UseGuards(JwtAuthGuard)
  @SubscribeMessage('join-user-room')
  handleJoinUserRoom(
    @ConnectedSocket() client: Socket,
    @User() user: any,
  ) {
    const userRoom = `user:${user.userId}`;
    client.join(userRoom);
    this.logger.log(`User ${user.userId} joined personal room: ${userRoom}`);
    client.emit('joined-user-room', { room: userRoom });
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.leave(data.room);
    this.logger.log(`Client ${client.id} left room: ${data.room}`);
    client.emit('left-room', { room: data.room });
  }

  @UseGuards(JwtAuthGuard)
  @SubscribeMessage('send-message')
  handleMessage(
    @MessageBody() data: { room: string; message: string },
    @ConnectedSocket() client: Socket,
    @User() user: any,
  ) {
    this.logger.log(
      `Message from ${user.email} in room ${data.room}: ${data.message}`,
    );

    this.server.to(data.room).emit('new-message', {
      user: user.email,
      message: data.message,
      timestamp: new Date(),
    });
  }

  // Method to send notifications to specific users or rooms
  sendNotification(room: string, notification: any) {
    this.server.to(room).emit('notification', notification);
  }

  // Method to broadcast to all connected clients
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }

  // Method to notify specific user
  notifyUser(userId: string, event: string, data: any) {
    const userRoom = `user:${userId}`;
    this.server.to(userRoom).emit(event, {
      ...data,
      timestamp: new Date(),
    });
    this.logger.log(`Notification sent to user ${userId}: ${event}`);
  }

  // Method to notify about file upload progress
  notifyUploadProgress(userId: string, progress: number, filename: string) {
    this.notifyUser(userId, 'upload-progress', {
      progress,
      filename,
      status: progress === 100 ? 'completed' : 'uploading',
    });
  }

  // Method to notify about processing status
  notifyProcessingStatus(userId: string, paperId: string, status: string, progress?: number) {
    this.notifyUser(userId, 'processing-status', {
      paperId,
      status,
      progress,
    });
  }
}
