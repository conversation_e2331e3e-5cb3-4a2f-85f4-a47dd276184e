import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { PapersService } from './papers.service';
import { UploadPaperDto, UpdatePaperStatusDto, PaperQueryDto } from './dto/upload-paper.dto';
import { PaperResponseDto, PaperWithAssetsResponseDto, PaperListResponseDto } from './dto/paper-response.dto';

@ApiTags('Papers')
@Controller('papers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PapersController {
  constructor(private readonly papersService: PapersService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a paper for character generation' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, type: PaperResponseDto })
  @UseInterceptors(FileInterceptor('file'))
  async uploadPaper(
    @Request() req: any,
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadPaperDto,
  ): Promise<PaperResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const userId = req.user.userId;
    return await this.papersService.uploadPaper(userId, file, uploadDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user papers with pagination and filtering' })
  @ApiResponse({ status: 200, type: PaperListResponseDto })
  async getUserPapers(
    @Request() req: any,
    @Query() query: PaperQueryDto,
  ): Promise<PaperListResponseDto> {
    const userId = req.user.userId;
    return await this.papersService.getUserPapers(userId, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get paper details with character assets' })
  @ApiResponse({ status: 200, type: PaperWithAssetsResponseDto })
  async getPaper(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<PaperWithAssetsResponseDto> {
    const userId = req.user.userId;
    return await this.papersService.getPaper(id, userId);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update paper processing status' })
  @ApiResponse({ status: 200, type: PaperResponseDto })
  async updatePaperStatus(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdatePaperStatusDto,
  ): Promise<PaperResponseDto> {
    const userId = req.user.userId;
    return await this.papersService.updatePaperStatus(id, updateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete paper and all associated assets' })
  @ApiResponse({ status: 200, description: 'Paper deleted successfully' })
  async deletePaper(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    const userId = req.user.userId;
    await this.papersService.deletePaper(id, userId);
    return { message: 'Paper deleted successfully' };
  }

  @Get(':id/download')
  @ApiOperation({ summary: 'Get download URL for paper' })
  @ApiResponse({ status: 200, description: 'Returns pre-signed download URL' })
  async getDownloadUrl(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<{ url: string; expiresIn: number }> {
    const userId = req.user.userId;
    const paper = await this.papersService.getPaper(id, userId);
    
    // This would typically use the storage service to generate a pre-signed URL
    // For now, return the existing storage URL
    return {
      url: paper.storageUrl || '',
      expiresIn: 3600, // 1 hour
    };
  }
}
