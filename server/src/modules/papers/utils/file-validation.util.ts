import { BadRequestException } from '@nestjs/common';

interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
  destination?: string;
  filename?: string;
  path?: string;
}

export interface FileValidationOptions {
  maxSize?: number; // in bytes
  allowedMimeTypes?: string[];
  allowedExtensions?: string[];
}

export class FileValidationUtil {
  private static readonly DEFAULT_MAX_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly DEFAULT_ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
  ];

  static validateFile(file: MulterFile, options: FileValidationOptions = {}): void {
    const {
      maxSize = this.DEFAULT_MAX_SIZE,
      allowedMimeTypes = this.DEFAULT_ALLOWED_MIME_TYPES,
      allowedExtensions,
    } = options;

    // Check if file exists
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Check file size
    if (file.size > maxSize) {
      throw new BadRequestException(
        `File size too large. Maximum allowed size is ${this.formatBytes(maxSize)}`
      );
    }

    // Check MIME type
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${allowedMimeTypes.join(', ')}`
      );
    }

    // Check file extension if specified
    if (allowedExtensions) {
      const fileExtension = this.getFileExtension(file.originalname);
      if (!allowedExtensions.includes(fileExtension)) {
        throw new BadRequestException(
          `Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`
        );
      }
    }

    // Additional security checks
    this.performSecurityChecks(file);
  }

  static validateImageFile(file: MulterFile): void {
    this.validateFile(file, {
      allowedMimeTypes: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
      ],
      allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    });
  }

  static validatePdfFile(file: MulterFile): void {
    this.validateFile(file, {
      allowedMimeTypes: ['application/pdf'],
      allowedExtensions: ['.pdf'],
    });
  }

  private static performSecurityChecks(file: MulterFile): void {
    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|scr|pif|com)$/i,
      /\.(php|jsp|asp|aspx)$/i,
      /\.(js|vbs|ps1)$/i,
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(file.originalname)) {
        throw new BadRequestException('Suspicious file type detected');
      }
    }

    // Check for null bytes in filename
    if (file.originalname.includes('\0')) {
      throw new BadRequestException('Invalid filename');
    }

    // Basic magic number validation for images
    if (file.mimetype.startsWith('image/')) {
      this.validateImageMagicNumbers(file);
    }
  }

  private static validateImageMagicNumbers(file: MulterFile): void {
    const buffer = file.buffer;
    if (!buffer || buffer.length < 4) {
      throw new BadRequestException('Invalid image file');
    }

    const magicNumbers = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'image/gif': [0x47, 0x49, 0x46],
      'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF header
    };

    const expectedMagic = magicNumbers[file.mimetype];
    if (expectedMagic) {
      for (let i = 0; i < expectedMagic.length; i++) {
        if (buffer[i] !== expectedMagic[i]) {
          // Special case for WebP - check for WEBP signature at offset 8
          if (file.mimetype === 'image/webp') {
            const webpSignature = [0x57, 0x45, 0x42, 0x50]; // WEBP
            let isValidWebp = true;
            for (let j = 0; j < webpSignature.length; j++) {
              if (buffer[8 + j] !== webpSignature[j]) {
                isValidWebp = false;
                break;
              }
            }
            if (!isValidWebp) {
              throw new BadRequestException('Invalid WebP file format');
            }
            return;
          }
          throw new BadRequestException(`Invalid ${file.mimetype} file format`);
        }
      }
    }
  }

  private static getFileExtension(filename: string): string {
    return filename.toLowerCase().substring(filename.lastIndexOf('.'));
  }

  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static generateSafeFilename(originalName: string, prefix?: string): string {
    // Remove unsafe characters and normalize
    const safeName = originalName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();
    
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    
    if (prefix) {
      return `${prefix}_${timestamp}_${random}_${safeName}`;
    }
    
    return `${timestamp}_${random}_${safeName}`;
  }
}
