import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsObject, IsEnum } from 'class-validator';
import { PaperStatus } from '@prisma/client';

export class UploadPaperDto {
  @ApiProperty({ description: 'Original filename', required: false })
  @IsOptional()
  @IsString()
  originalName?: string;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdatePaperStatusDto {
  @ApiProperty({ enum: PaperStatus, description: 'Paper processing status' })
  @IsEnum(PaperStatus)
  status: PaperStatus;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class PaperQueryDto {
  @ApiProperty({ description: 'Filter by status', required: false })
  @IsOptional()
  @IsEnum(PaperStatus)
  status?: PaperStatus;

  @ApiProperty({ description: 'Search by filename', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', required: false, default: 10 })
  @IsOptional()
  limit?: number = 10;
}
