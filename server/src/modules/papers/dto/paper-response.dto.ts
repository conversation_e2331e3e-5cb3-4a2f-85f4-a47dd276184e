import { ApiProperty } from '@nestjs/swagger';
import { PaperStatus, AssetType } from '@prisma/client';

export class PaperResponseDto {
  @ApiProperty({ description: 'Paper ID' })
  id: string;

  @ApiProperty({ description: 'Filename in storage' })
  filename: string;

  @ApiProperty({ description: 'Original filename' })
  originalName: string;

  @ApiProperty({ description: 'MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'File size in bytes' })
  size: number;

  @ApiProperty({ description: 'Storage key' })
  storageKey: string;

  @ApiProperty({ description: 'Storage bucket' })
  storageBucket: string;

  @ApiProperty({ description: 'Storage URL', required: false })
  storageUrl?: string;

  @ApiProperty({ enum: PaperStatus, description: 'Processing status' })
  status: PaperStatus;

  @ApiProperty({ description: 'Processing completion date', required: false })
  processedAt?: Date;

  @ApiProperty({ description: 'User ID who uploaded' })
  userId: string;

  @ApiProperty({ description: 'Additional metadata', required: false })
  metadata?: any;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;
}

export class CharacterAssetResponseDto {
  @ApiProperty({ description: 'Asset ID' })
  id: string;

  @ApiProperty({ description: 'Filename in storage' })
  filename: string;

  @ApiProperty({ enum: AssetType, description: 'Asset type' })
  assetType: AssetType;

  @ApiProperty({ description: 'MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'File size in bytes' })
  size: number;

  @ApiProperty({ description: 'Storage key' })
  storageKey: string;

  @ApiProperty({ description: 'Storage bucket' })
  storageBucket: string;

  @ApiProperty({ description: 'Storage URL', required: false })
  storageUrl?: string;

  @ApiProperty({ description: 'Source paper ID' })
  paperId: string;

  @ApiProperty({ description: 'Processing parameters', required: false })
  processingParams?: any;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;
}

export class PaperWithAssetsResponseDto extends PaperResponseDto {
  @ApiProperty({ type: [CharacterAssetResponseDto], description: 'Generated character assets' })
  characterAssets: CharacterAssetResponseDto[];
}

export class PaperListResponseDto {
  @ApiProperty({ type: [PaperResponseDto], description: 'List of papers' })
  papers: PaperResponseDto[];

  @ApiProperty({ description: 'Total number of papers' })
  total: number;

  @ApiProperty({ description: 'Current page' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total pages' })
  totalPages: number;
}
