import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from 'src/database/prisma.service';
import { StorageService } from 'src/modules/storage/storage.service';
import { QueueService } from 'src/queue/queue.service';
import { PaperStatus, AssetType } from '@prisma/client';
import { UploadPaperDto, UpdatePaperStatusDto, PaperQueryDto } from './dto/upload-paper.dto';
import { PaperResponseDto, PaperWithAssetsResponseDto, PaperListResponseDto, CharacterAssetResponseDto } from './dto/paper-response.dto';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PapersService {
  private readonly logger = new Logger(PapersService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
    private readonly queueService: QueueService,
  ) {}

  async uploadPaper(
    userId: string,
    file: Express.Multer.File,
    uploadDto: UploadPaperDto,
  ): Promise<PaperResponseDto> {
    // Validate file type
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Invalid file type. Only images and PDFs are allowed.');
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalname);
    const filename = `${uuidv4()}${fileExtension}`;
    const storageKey = `papers/${userId}/${filename}`;

    try {
      // Upload to MinIO
      const uploadResult = await this.storageService.uploadFile(
        this.storageService.getPapersBucket(),
        storageKey,
        file.buffer,
        file.mimetype,
        {
          'user-id': userId,
          'original-name': file.originalname,
        },
      );

      // Save to database
      const paper = await this.prisma.paper.create({
        data: {
          filename,
          originalName: uploadDto.originalName || file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          storageKey,
          storageBucket: uploadResult.bucket,
          storageUrl: uploadResult.url,
          userId,
          metadata: uploadDto.metadata,
          status: PaperStatus.UPLOADED,
        },
      });

      // Queue for processing
      await this.queueService.addJob('file-processing', 'process-paper', {
        paperId: paper.id,
        userId,
        storageKey,
        mimeType: file.mimetype,
      });

      this.logger.log(`Paper uploaded successfully: ${paper.id}`);
      return this.mapToResponseDto(paper);
    } catch (error) {
      this.logger.error(`Failed to upload paper for user ${userId}:`, error);
      throw new BadRequestException('Failed to upload paper');
    }
  }

  async getPaper(id: string, userId?: string): Promise<PaperWithAssetsResponseDto> {
    const paper = await this.prisma.paper.findFirst({
      where: {
        id,
        ...(userId && { userId }),
      },
      include: {
        characterAssets: true,
      },
    });

    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    return this.mapToResponseWithAssetsDto(paper);
  }

  async getUserPapers(userId: string, query: PaperQueryDto): Promise<PaperListResponseDto> {
    const { status, search, page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const where = {
      userId,
      ...(status && { status }),
      ...(search && {
        OR: [
          { filename: { contains: search, mode: 'insensitive' as const } },
          { originalName: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
    };

    const [papers, total] = await Promise.all([
      this.prisma.paper.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.paper.count({ where }),
    ]);

    return {
      papers: papers.map(paper => this.mapToResponseDto(paper)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updatePaperStatus(
    id: string,
    updateDto: UpdatePaperStatusDto,
    userId?: string,
  ): Promise<PaperResponseDto> {
    const paper = await this.prisma.paper.findFirst({
      where: {
        id,
        ...(userId && { userId }),
      },
    });

    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    const updatedPaper = await this.prisma.paper.update({
      where: { id },
      data: {
        status: updateDto.status,
        metadata: updateDto.metadata ? { ...paper.metadata, ...updateDto.metadata } : paper.metadata,
        processedAt: updateDto.status === PaperStatus.PROCESSED ? new Date() : paper.processedAt,
      },
    });

    this.logger.log(`Paper status updated: ${id} -> ${updateDto.status}`);
    return this.mapToResponseDto(updatedPaper);
  }

  async deletePaper(id: string, userId?: string): Promise<void> {
    const paper = await this.prisma.paper.findFirst({
      where: {
        id,
        ...(userId && { userId }),
      },
      include: {
        characterAssets: true,
      },
    });

    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    try {
      // Delete from storage
      await this.storageService.deleteFile(paper.storageBucket, paper.storageKey);

      // Delete character assets from storage
      for (const asset of paper.characterAssets) {
        await this.storageService.deleteFile(asset.storageBucket, asset.storageKey);
      }

      // Delete from database (cascade will handle character assets)
      await this.prisma.paper.delete({ where: { id } });

      this.logger.log(`Paper deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete paper ${id}:`, error);
      throw new BadRequestException('Failed to delete paper');
    }
  }

  async createCharacterAsset(
    paperId: string,
    assetType: AssetType,
    file: Buffer,
    filename: string,
    mimeType: string,
    processingParams?: any,
  ): Promise<CharacterAssetResponseDto> {
    const paper = await this.prisma.paper.findUnique({ where: { id: paperId } });
    if (!paper) {
      throw new NotFoundException('Paper not found');
    }

    const storageKey = `assets/${paperId}/${assetType.toLowerCase()}/${filename}`;

    try {
      // Upload to MinIO
      const uploadResult = await this.storageService.uploadFile(
        this.storageService.getAssetsBucket(),
        storageKey,
        file,
        mimeType,
        {
          'paper-id': paperId,
          'asset-type': assetType,
        },
      );

      // Save to database
      const asset = await this.prisma.characterAsset.create({
        data: {
          filename,
          assetType,
          mimeType,
          size: file.length,
          storageKey,
          storageBucket: uploadResult.bucket,
          storageUrl: uploadResult.url,
          paperId,
          processingParams,
        },
      });

      this.logger.log(`Character asset created: ${asset.id} for paper ${paperId}`);
      return this.mapAssetToResponseDto(asset);
    } catch (error) {
      this.logger.error(`Failed to create character asset for paper ${paperId}:`, error);
      throw new BadRequestException('Failed to create character asset');
    }
  }

  private mapToResponseDto(paper: any): PaperResponseDto {
    return {
      id: paper.id,
      filename: paper.filename,
      originalName: paper.originalName,
      mimeType: paper.mimeType,
      size: paper.size,
      storageKey: paper.storageKey,
      storageBucket: paper.storageBucket,
      storageUrl: paper.storageUrl,
      status: paper.status,
      processedAt: paper.processedAt,
      userId: paper.userId,
      metadata: paper.metadata,
      createdAt: paper.createdAt,
      updatedAt: paper.updatedAt,
    };
  }

  private mapToResponseWithAssetsDto(paper: any): PaperWithAssetsResponseDto {
    return {
      ...this.mapToResponseDto(paper),
      characterAssets: paper.characterAssets?.map(asset => this.mapAssetToResponseDto(asset)) || [],
    };
  }

  private mapAssetToResponseDto(asset: any): CharacterAssetResponseDto {
    return {
      id: asset.id,
      filename: asset.filename,
      assetType: asset.assetType,
      mimeType: asset.mimeType,
      size: asset.size,
      storageKey: asset.storageKey,
      storageBucket: asset.storageBucket,
      storageUrl: asset.storageUrl,
      paperId: asset.paperId,
      processingParams: asset.processingParams,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    };
  }
}
