// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String  @id @default(cuid())
  email     String  @unique
  password  String
  firstName String?
  lastName  String?
  roles     Role[]  @default([USER])
  isActive  Boolean @default(true)

  characters Character[]
  papers     Paper[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum Role {
  USER
  ADMIN
  MODERATOR
}

model Stage {
  id   String @id @default(cuid())
  name String @unique

  apiKeyHash String @unique

  maxCharacters Int @default(20)
  characterTTL  Int @default(60)

  projections Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("stages")
}

model CharacterType {
  id         String      @id @default(cuid())
  name       String      @unique
  scale      Float       @default(1)
  characters Character[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("character_types")
}

model Character {
  id String @id @default(cuid())

  characterType   CharacterType @relation(fields: [characterTypeId], references: [id])
  characterTypeId String

  user   User?   @relation(fields: [userId], references: [id])
  userId String?

  projections Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("characters")
}

model Projection {
  id String @id @default(cuid())

  character   Character @relation(fields: [characterId], references: [id])
  characterId String

  stage   Stage  @relation(fields: [stageId], references: [id])
  stageId String

  requestedAt DateTime  @default(now())
  onScreenAt  DateTime?
  removedAt   DateTime?

  status ProjectionStatus @default(WAITING)

  @@map("projections")
}

enum ProjectionStatus {
  WAITING
  ON_SCREEN
  REMOVED
}

model Paper {
  id            String  @id @default(cuid())
  filename      String
  originalName  String
  mimeType      String
  size          Int
  storageKey    String  @unique
  storageBucket String
  storageUrl    String?

  // Processing status
  status      PaperStatus @default(UPLOADED)
  processedAt DateTime?

  // User who uploaded the paper
  user   User   @relation(fields: [userId], references: [id])
  userId String

  // Generated character assets
  characterAssets CharacterAsset[]

  // Metadata
  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("papers")
}

enum PaperStatus {
  UPLOADED
  PROCESSING
  PROCESSED
  FAILED
}

model CharacterAsset {
  id            String    @id @default(cuid())
  filename      String
  assetType     AssetType
  mimeType      String
  size          Int
  storageKey    String    @unique
  storageBucket String
  storageUrl    String?

  // Source paper
  paper   Paper  @relation(fields: [paperId], references: [id])
  paperId String

  // Processing metadata
  processingParams Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("character_assets")
}

enum AssetType {
  SPRITE
  ANIMATION
  TEXTURE
  MASK
  THUMBNAIL
}
