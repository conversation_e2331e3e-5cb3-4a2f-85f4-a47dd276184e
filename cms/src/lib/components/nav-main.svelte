<script lang="ts">
	import { page } from '$app/state';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { cn } from '$lib/utils';
	import type { Component } from 'svelte';

	let {
		items
	}: {
		items: {
			title: string;
			url: string;
			Icon?: Component;
		}[];
	} = $props();
</script>

<Sidebar.Menu class="px-2">
	{#each items ?? [] as item (item.title)}
		{@const isActive = page.url.pathname.startsWith(item.url)}
		<Sidebar.MenuSubItem>
			<Sidebar.MenuSubButton {isActive}>
				{#snippet child({ props })}
					<a href={item.url} {...props}>
						{#if item.Icon}
							<item.Icon class={cn({ 'bg-accent': isActive })} />
						{/if}
						<span>{item.title}</span>
					</a>
				{/snippet}
			</Sidebar.MenuSubButton>
		</Sidebar.MenuSubItem>
	{/each}
</Sidebar.Menu>
