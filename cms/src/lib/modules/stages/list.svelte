<script lang="ts">
	import type { Stage } from '$lib/modules/stages/data/schemas';
	import DataTable from './components/data-table.svelte';

	let { data }: { data: Stage[] } = $props();
</script>

<div class="hidden h-full flex-1 flex-col gap-8 p-8 md:flex">
	<div class="flex flex-col gap-1">
		<h2 class="text-2xl font-semibold tracking-tight">Stages</h2>
		<p class="text-muted-foreground">Manage your stages</p>
	</div>
	<DataTable {data} />
</div>
